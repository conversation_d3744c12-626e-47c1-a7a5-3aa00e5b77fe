"use client"

import React from "react"

import { useEffect, useRef, useState, useCallback } from "react"
import { motion, useScroll, useTransform, useSpring, AnimatePresence } from "framer-motion"
import { Search, BarChart3, Settings, CheckCircle, ArrowRight, Volume2, Waves, Building, Users } from "lucide-react"

function HeroSection() {
  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute inset-0 bg-[url('/placeholder.svg?height=1080&width=1920')] bg-cover bg-center opacity-5" />
      </div>

      <div className="relative z-10 text-center max-w-6xl mx-auto px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-block"
            >
              <div className="bg-blue-500/10 backdrop-blur-sm border border-blue-400/20 rounded-full px-6 py-2 mb-6">
                <span className="text-blue-300 text-sm font-medium flex items-center gap-2">
                  <Volume2 className="w-4 h-4" />
                  Professional Acoustic Solutions
                </span>
              </div>
            </motion.div>

            <h1 className="text-7xl md:text-8xl font-bold text-white mb-6 leading-tight">
              Porter
              <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                Acoustics
              </span>
            </h1>

            <p className="text-2xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Transforming spaces through innovative acoustic solutions and expert sound engineering that enhance both form and function
            </p>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <motion.button
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-full font-semibold text-lg shadow-lg shadow-blue-500/25 flex items-center gap-2"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              Explore Our Work <ArrowRight className="w-5 h-5" />
            </motion.button>

            <motion.button
              className="border-2 border-white/30 hover:border-white/50 text-white px-10 py-4 rounded-full font-semibold text-lg backdrop-blur-sm hover:bg-white/5"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              Get Consultation
            </motion.button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.8 }}
            className="pt-8"
          >
            <p className="text-slate-400 text-sm mb-4">Scroll to explore our services</p>
            <div className="flex justify-center">
              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
              >
                <div className="w-1 h-3 bg-white/60 rounded-full mt-2"></div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

function ServicesSection() {
  const services = [
    {
      icon: Building,
      title: "Architectural Acoustics",
      description: "Custom acoustic design for commercial and residential spaces that balances aesthetics with optimal sound performance",
      features: ["Room acoustics", "Noise control", "Sound isolation"]
    },
    {
      icon: Waves,
      title: "Sound Engineering",
      description: "Professional audio system design and optimization for venues, studios, and commercial applications",
      features: ["Audio systems", "Sound reinforcement", "Studio design"]
    },
    {
      icon: Users,
      title: "Consultation Services",
      description: "Expert acoustic consulting for complex projects with comprehensive analysis and tailored solutions",
      features: ["Acoustic analysis", "Project planning", "Technical support"]
    },
  ]

  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-800 via-slate-900 to-blue-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-72 h-72 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="bg-blue-500/10 backdrop-blur-sm border border-blue-400/20 rounded-full px-6 py-2 mb-6 inline-block">
            <span className="text-blue-300 text-sm font-medium">What We Offer</span>
          </div>
          <h2 className="text-6xl font-bold text-white mb-6 leading-tight">
            Our
            <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
              Services
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            Comprehensive acoustic solutions designed to transform your space and enhance your audio experience
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                className="group bg-white/5 backdrop-blur-sm border border-white/10 hover:border-blue-400/50 p-8 rounded-2xl transition-all duration-500 hover:bg-white/10"
                whileHover={{ y: -5 }}
              >
                <div className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 w-16 h-16 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <IconComponent className="w-8 h-8 text-blue-400" />
                </div>

                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-blue-300 transition-colors duration-300">
                  {service.title}
                </h3>

                <p className="text-slate-300 mb-6 leading-relaxed">
                  {service.description}
                </p>

                <div className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                      <span className="text-slate-400 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

const processSteps = [
  {
    id: "discovery",
    icon: Search,
    title: "Discovery",
    description:
      "We begin by understanding your unique acoustic challenges and requirements through comprehensive site analysis and consultation.",
  },
  {
    id: "analysis",
    icon: BarChart3,
    title: "Analysis & Design",
    description:
      "Our experts analyze acoustic data and create custom solutions tailored to your specific environment and needs.",
  },
  {
    id: "implementation",
    icon: Settings,
    title: "Implementation",
    description:
      "Professional installation and integration of acoustic solutions with minimal disruption to your operations.",
  },
  {
    id: "verification",
    icon: CheckCircle,
    title: "Verification & Support",
    description:
      "Post-installation testing and ongoing support to ensure optimal acoustic performance and client satisfaction.",
  },
]

// Generate sine wave path with proper curves like the reference image
// Using fixed precision to avoid hydration mismatches
const generateSineWavePath = (width: number, height: number, frequency = 2, amplitude = 80) => {
  const points = []
  const steps = 300 // More steps for smoother curve
  for (let i = 0; i <= steps; i++) {
    const x = (i / steps) * width
    const y = height / 2 + Math.sin((i / steps) * Math.PI * frequency) * amplitude
    // Round to avoid hydration mismatches
    points.push(`${Math.round(x * 100) / 100},${Math.round(y * 100) / 100}`)
  }
  return `M ${points.join(" L ")}`
}

// Calculate position along sine wave for any progress value
const getPositionOnSineWave = (
  progress: number,
  totalWidth = 900,
  totalHeight = 400,
  frequency = 2,
  amplitude = 80,
) => {
  const x = progress * totalWidth
  const y = totalHeight / 2 + Math.sin(progress * Math.PI * frequency) * amplitude
  // Round to avoid hydration mismatches
  return {
    x: Math.round(x * 100) / 100,
    y: Math.round(y * 100) / 100
  }
}

function ProcessSection({
  scrollProgress,
  onAnimationComplete,
  onProgressUpdate
}: {
  scrollProgress: any
  onAnimationComplete: () => void
  onProgressUpdate?: (progress: number) => void
}) {
  const [currentStep, setCurrentStep] = useState(-1)
  const [isAtStop, setIsAtStop] = useState(false)
  const [snappedProgress, setSnappedProgress] = useState(0)
  const [animationComplete, setAnimationComplete] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [internalScrollProgress, setInternalScrollProgress] = useState(0)
  const [iconPosition, setIconPosition] = useState({ x: 0, y: 200 })

  const processProgress = useSpring(0, { stiffness: 100, damping: 30 })

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Update icon position when internal scroll progress changes
  useEffect(() => {
    const newPosition = getPositionOnSineWave(internalScrollProgress, 900, 400, 2, 80)
    setIconPosition(newPosition)

    // Check if we're at a stop position
    const stepProgress = internalScrollProgress * (processSteps.length - 1)
    const nearestStep = Math.round(stepProgress)
    const distanceToStep = Math.abs(stepProgress - nearestStep)
    const snapThreshold = 0.15

    if (distanceToStep < snapThreshold && nearestStep >= 0 && nearestStep < processSteps.length) {
      setCurrentStep(nearestStep)
      setIsAtStop(true)
      const snappedStepProgress = nearestStep / (processSteps.length - 1)
      setSnappedProgress(snappedStepProgress)
    } else {
      setCurrentStep(-1)
      setIsAtStop(false)
      setSnappedProgress(internalScrollProgress)
    }
  }, [internalScrollProgress])

  // Manual scroll-controlled animation through the process steps
  useEffect(() => {
    if (!scrollProgress || typeof scrollProgress.on !== 'function') {
      return
    }

    const unsubscribe = scrollProgress.on("change", (latest: number) => {
      const waveProgress = Math.max(0, Math.min(1, latest))
      setInternalScrollProgress(waveProgress)

      // Always update the process progress to move the icon
      processProgress.set(waveProgress)

      // Check if we've reached the end
      const stepProgress = waveProgress * (processSteps.length - 1)
      const nearestStep = Math.round(stepProgress)
      if (nearestStep === processSteps.length - 1 && waveProgress >= 0.9 && !animationComplete) {
        setAnimationComplete(true)
        onAnimationComplete()
      }
    })

    // Set initial value
    const initialValue = scrollProgress.get()
    setInternalScrollProgress(initialValue)

    return unsubscribe
  }, [scrollProgress, processProgress, animationComplete, onAnimationComplete])

  const sineWavePath = generateSineWavePath(900, 400, 2, 80)

  // Calculate popup position based on sine wave position
  const getPopupPosition = (iconPosition: { x: number; y: number }) => {
    const centerY = 200 // Center of the sine wave (400/2)
    const isIconAtTop = iconPosition.y < centerY

    return {
      x: iconPosition.x,
      y: isIconAtTop ? iconPosition.y + 120 : iconPosition.y - 120, // Position opposite to icon
      isBelow: isIconAtTop,
    }
  }

  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-8 w-full">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-5xl font-bold text-white mb-6">Our Process</h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            A systematic approach to delivering exceptional acoustic solutions
          </p>
          <div className="mt-8 text-sm text-slate-400 max-w-md mx-auto">
            <div className="flex items-center justify-center gap-2 mb-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span>Use scroll wheel or arrow keys to explore our process</span>
            </div>
          </div>
        </motion.div>

        <div className="relative flex justify-center w-full">
          {/* Container for sine wave and popups */}
          <div className="relative w-full max-w-5xl mx-auto px-4">
            {isClient && (
              <svg
                width="100%"
                height="400"
                viewBox="0 0 900 400"
                className="w-full h-auto"
                preserveAspectRatio="xMidYMid meet"
              >
              {/* Animated sine wave path */}
              <motion.path
                d={sineWavePath}
                stroke="url(#gradient)"
                strokeWidth="4"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 3, ease: "easeInOut" }}
              />

              {/* Gradient definition */}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="50%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#06b6d4" />
                </linearGradient>
              </defs>

              {/* Step position markers */}
              {processSteps.map((step, index) => {
                const stepProgress = index / (processSteps.length - 1)
                const position = getPositionOnSineWave(stepProgress, 900, 400, 2, 80)

                return (
                  <motion.circle
                    key={step.id}
                    cx={position.x}
                    cy={position.y}
                    r="8"
                    fill={currentStep === index ? "#3b82f6" : "#64748b"}
                    stroke={currentStep === index ? "#ffffff" : "transparent"}
                    strokeWidth="2"
                    initial={{ scale: 0 }}
                    animate={{
                      scale: 1,
                      fill: currentStep === index ? "#3b82f6" : "#64748b",
                    }}
                    transition={{ delay: index * 0.3, duration: 0.3 }}
                  />
                )
              })}

              {/* Moving icon that follows the sine wave */}
              <g>
                <circle
                  r="35"
                  fill="white"
                  stroke="#3b82f6"
                  strokeWidth="4"
                  cx={iconPosition.x}
                  cy={iconPosition.y}
                  style={{
                    filter: "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.4))",
                    transition: "cx 0.3s ease-out, cy 0.3s ease-out"
                  }}
                />
                <foreignObject
                  width="36"
                  height="36"
                  x={iconPosition.x - 18}
                  y={iconPosition.y - 18}
                  style={{
                    transition: "x 0.3s ease-out, y 0.3s ease-out"
                  }}
                >
                  <div className="w-full h-full flex items-center justify-center">
                    {React.createElement(currentStep >= 0 ? processSteps[currentStep]?.icon : Search, {
                      className: "w-9 h-9 text-blue-500",
                    })}
                  </div>
                </foreignObject>
              </g>
            </svg>
            )}

            {/* Fallback for server-side rendering */}
            {!isClient && (
              <div className="w-[900px] h-[300px] flex items-center justify-center bg-slate-800/50 rounded-lg">
                <div className="text-white text-lg">Loading process visualization...</div>
              </div>
            )}

            {/* Popup positioned relative to icon on sine wave */}
            <AnimatePresence mode="wait">
              {isClient && isAtStop && currentStep >= 0 && (
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 25,
                    duration: 0.4,
                  }}
                  className="absolute z-10 pointer-events-none"
                  style={{
                    left: `${iconPosition.x}px`,
                    top: `${getPopupPosition(iconPosition).y}px`,
                    transform: "translate(-50%, -50%)",
                  }}
                >
                  <div className="bg-white/15 backdrop-blur-md p-6 rounded-2xl border border-blue-400/50 shadow-2xl shadow-blue-500/20 max-w-xs text-center">
                    <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center mb-4 mx-auto shadow-lg shadow-blue-500/50">
                      {React.createElement(processSteps[currentStep].icon, {
                        className: "w-6 h-6 text-white",
                      })}
                    </div>
                    <h3 className="text-lg font-bold text-white mb-3">{processSteps[currentStep].title}</h3>
                    <p className="text-slate-200 text-sm leading-relaxed">{processSteps[currentStep].description}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  )
}

function ContactSection() {
  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-slate-900 to-purple-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-6xl mx-auto px-8 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-12"
        >
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              <div className="bg-purple-500/10 backdrop-blur-sm border border-purple-400/20 rounded-full px-6 py-2 mb-6 inline-block">
                <span className="text-purple-300 text-sm font-medium">Ready to Get Started?</span>
              </div>
            </motion.div>

            <h2 className="text-6xl md:text-7xl font-bold text-white mb-6 leading-tight">
              Let's Create
              <span className="block bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                Something Amazing
              </span>
            </h2>

            <p className="text-2xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Ready to transform your space with expert acoustic solutions? Let's discuss your project and bring your vision to life.
            </p>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <motion.button
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-12 py-5 rounded-full font-semibold text-lg shadow-lg shadow-purple-500/25"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              Start Your Project
            </motion.button>

            <motion.button
              className="border-2 border-white/30 hover:border-white/50 text-white px-12 py-5 rounded-full font-semibold text-lg backdrop-blur-sm hover:bg-white/5"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              View Portfolio
            </motion.button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.8 }}
            className="pt-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                <h3 className="text-white font-semibold mb-2">Email Us</h3>
                <p className="text-slate-300 text-sm"><EMAIL></p>
              </div>
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                <h3 className="text-white font-semibold mb-2">Call Us</h3>
                <p className="text-slate-300 text-sm">+1 (555) 123-4567</p>
              </div>
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                <h3 className="text-white font-semibold mb-2">Visit Us</h3>
                <p className="text-slate-300 text-sm">123 Sound Street, Audio City</p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default function Component() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [currentSection, setCurrentSection] = useState(0)
  const [isProcessAnimationComplete, setIsProcessAnimationComplete] = useState(false)
  const [isScrollLocked, setIsScrollLocked] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const [isScrolling, setIsScrolling] = useState(false)
  const [processScrollProgress, setProcessScrollProgress] = useState(0)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const totalSections = 4

  // Handle scroll events for horizontal navigation with debouncing
  const handleScroll = useCallback((event: WheelEvent) => {
    event.preventDefault()

    const delta = event.deltaY || event.deltaX

    // Accept any non-zero scroll movement
    if (delta === 0) return

    // Special handling for process section (section 2)
    if (currentSection === 2) {
      // In process section, snap to next/previous step
      const scrollDirection = delta > 0 ? 1 : -1

      setProcessScrollProgress(prev => {
        const currentStep = Math.round(prev * 3) // 3 intervals for 4 steps
        let targetStep

        if (scrollDirection > 0) {
          targetStep = Math.min(currentStep + 1, 3)
        } else {
          targetStep = Math.max(currentStep - 1, 0)
        }

        const newProgress = targetStep / 3

        // Handle section transitions
        if (newProgress >= 1 && scrollDirection > 0 && !isScrolling) {
          setIsScrolling(true)
          setTimeout(() => {
            setCurrentSection(3)
            setIsScrolling(false)
          }, 300)
        } else if (newProgress <= 0 && scrollDirection < 0 && !isScrolling) {
          setIsScrolling(true)
          setTimeout(() => {
            setCurrentSection(1)
            setIsScrolling(false)
          }, 300)
        }

        return newProgress
      })
      return
    }

    // Normal section navigation for other sections
    if (isScrolling) return

    // Prevent rapid scrolling
    setIsScrolling(true)

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    if (delta > 0 && currentSection < totalSections - 1) {
      // Scrolling right/down
      setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
    } else if (delta < 0 && currentSection > 0) {
      // Scrolling left/up
      setCurrentSection(prev => Math.max(prev - 1, 0))
    }

    // Reset scrolling state after delay
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false)
    }, 800) // 800ms delay before allowing next scroll

  }, [currentSection, isScrolling, processScrollProgress])

  // Handle process animation completion
  const handleProcessAnimationComplete = useCallback(() => {
    setIsProcessAnimationComplete(true)
    setIsScrollLocked(false)
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault()
        if (currentSection === 2) {
          // In process section, snap to next step (4 steps total: 0, 0.33, 0.67, 1)
          setProcessScrollProgress(prev => {
            const currentStep = Math.round(prev * 3) // 3 intervals for 4 steps
            const nextStep = Math.min(currentStep + 1, 3)
            const newProgress = nextStep / 3

            if (newProgress >= 1) {
              setCurrentSection(3) // Move to next section when complete
            }
            return newProgress
          })
        } else if (currentSection < totalSections - 1) {
          setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
        }
        break
      case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault()
        if (currentSection === 2) {
          // In process section, snap to previous step (4 steps total: 0, 0.33, 0.67, 1)
          setProcessScrollProgress(prev => {
            const currentStep = Math.round(prev * 3) // 3 intervals for 4 steps
            const prevStep = Math.max(currentStep - 1, 0)
            const newProgress = prevStep / 3

            if (newProgress <= 0) {
              setCurrentSection(1) // Move to previous section when at start
            }
            return newProgress
          })
        } else if (currentSection > 0) {
          setCurrentSection(prev => Math.max(prev - 1, 0))
        }
        break
      case 'Home':
        event.preventDefault()
        setCurrentSection(0)
        setProcessScrollProgress(0)
        break
      case 'End':
        event.preventDefault()
        setCurrentSection(totalSections - 1)
        setProcessScrollProgress(1)
        break
    }
  }, [currentSection, totalSections])

  // Handle touch events for mobile swipe navigation
  const handleTouchStart = useCallback((event: TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(event.targetTouches[0].clientX)
  }, [])

  const handleTouchMove = useCallback((event: TouchEvent) => {
    setTouchEnd(event.targetTouches[0].clientX)
  }, [])

  const handleTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd || isScrollLocked) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe && currentSection < totalSections - 1) {
      if (currentSection === 2 && !isProcessAnimationComplete) {
        setIsScrollLocked(true)
        return
      }
      setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
    }

    if (isRightSwipe && currentSection > 0) {
      setCurrentSection(prev => Math.max(prev - 1, 0))
    }
  }, [touchStart, touchEnd, currentSection, isProcessAnimationComplete, isScrollLocked, totalSections])

  // Add scroll, keyboard, and touch event listeners
  useEffect(() => {
    const container = containerRef.current

    const testScrollHandler = (e: WheelEvent) => {
      e.preventDefault()
      handleScroll(e)
    }

    if (container) {
      container.addEventListener('wheel', testScrollHandler, { passive: false })
      container.addEventListener('touchstart', handleTouchStart, { passive: false })
      container.addEventListener('touchmove', handleTouchMove, { passive: false })
      container.addEventListener('touchend', handleTouchEnd, { passive: false })
    }
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      if (container) {
        container.removeEventListener('wheel', testScrollHandler)
        container.removeEventListener('touchstart', handleTouchStart)
        container.removeEventListener('touchmove', handleTouchMove)
        container.removeEventListener('touchend', handleTouchEnd)
      }
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleScroll, handleKeyDown, handleTouchStart, handleTouchMove, handleTouchEnd, currentSection])

  // Calculate transform based on current section
  const xTransform = `translateX(-${currentSection * 100}vw)`

  // Create scroll progress spring for process section
  const processScrollProgressSpring = useSpring(processScrollProgress, { stiffness: 200, damping: 25 })

  // Force spring to update when state changes
  useEffect(() => {
    processScrollProgressSpring.set(processScrollProgress)
  }, [processScrollProgress, processScrollProgressSpring])



  // Reset process progress when entering/leaving process section
  useEffect(() => {
    if (currentSection === 2) {
      // Entering process section - start from beginning if coming from left, or maintain progress
      if (processScrollProgress === 0) {
        setProcessScrollProgress(0)
      }
    } else if (currentSection < 2) {
      // Going to earlier sections - reset progress
      setProcessScrollProgress(0)
    }
  }, [currentSection, processScrollProgress])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className="h-screen overflow-hidden relative"
      style={{
        cursor: isScrollLocked ? 'not-allowed' : (isScrolling ? 'grabbing' : 'grab')
      }}
    >
      {/* Horizontal sections container */}
      <div
        className="flex h-screen transition-transform duration-1000 ease-out"
        style={{
          transform: xTransform,
          width: `${totalSections * 100}vw`
        }}
      >
        {/* Section 1: Hero */}
        <div className="w-screen h-screen flex-shrink-0">
          <HeroSection />
        </div>

        {/* Section 2: Services */}
        <div className="w-screen h-screen flex-shrink-0">
          <ServicesSection />
        </div>

        {/* Section 3: Process */}
        <div className="w-screen h-screen flex-shrink-0 relative">
          <ProcessSection
            scrollProgress={processScrollProgressSpring}
            onAnimationComplete={handleProcessAnimationComplete}
            onProgressUpdate={setProcessScrollProgress}
          />
        </div>

        {/* Section 4: Contact */}
        <div className="w-screen h-screen flex-shrink-0">
          <ContactSection />
        </div>
      </div>

      {/* Elegant section indicators */}
      <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-white/10 backdrop-blur-md px-4 py-3 rounded-full border border-white/20">
          <div className="flex items-center space-x-4">
            {['Home', 'Services', 'Process', 'Contact'].map((label, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentSection(index)
                  if (index !== 2) {
                    setProcessScrollProgress(index > 2 ? 1 : 0)
                  }
                }}
                className={`group flex items-center gap-2 px-3 py-1.5 rounded-full transition-all duration-300 ${
                  currentSection === index
                    ? 'bg-white/20 text-white'
                    : 'text-white/60 hover:text-white/80 hover:bg-white/10'
                }`}
                title={label}
              >
                <div className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  currentSection === index
                    ? 'bg-blue-400 scale-125'
                    : 'bg-white/40 group-hover:bg-white/60'
                }`} />
                <span className="text-xs font-medium hidden sm:block">{label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Clean progress indicator for process section */}
      {currentSection === 2 && (
        <div className="fixed top-8 left-1/2 transform -translate-x-1/2 z-40">
          <div className="bg-white/10 backdrop-blur-md px-6 py-3 rounded-full border border-white/20">
            <div className="flex items-center gap-3">
              <span className="text-white text-sm font-medium">Process</span>
              <div className="w-32 h-1.5 bg-white/20 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-400 to-purple-400 transition-all duration-500 ease-out"
                  style={{ width: `${processScrollProgress * 100}%` }}
                />
              </div>
              <span className="text-white/80 text-xs font-medium min-w-[3ch]">
                {Math.round(processScrollProgress * 100)}%
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
