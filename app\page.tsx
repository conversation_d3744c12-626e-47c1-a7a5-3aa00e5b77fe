"use client"

import React from "react"

import { useEffect, useRef, useState, useCallback } from "react"
import { motion, useScroll, useTransform, useSpring, AnimatePresence } from "framer-motion"
import { Search, BarChart3, Settings, CheckCircle, ArrowRight, Volume2, Waves, Building, Users } from "lucide-react"

function HeroSection() {
  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/placeholder.svg?height=1080&width=1920')] bg-cover bg-center opacity-10" />
      <div className="relative z-10 text-center max-w-4xl mx-auto px-8">
        <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 1 }}>
          <Volume2 className="w-16 h-16 text-blue-400 mx-auto mb-6" />
          <h1 className="text-6xl font-bold text-white mb-6">Porter Acoustics</h1>
          <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Transforming spaces through innovative acoustic solutions and expert sound engineering
          </p>
          <motion.button
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold flex items-center gap-2 mx-auto"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Explore Our Work <ArrowRight className="w-5 h-5" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

function ServicesSection() {
  const services = [
    {
      icon: Building,
      title: "Architectural Acoustics",
      description: "Custom acoustic design for commercial and residential spaces",
    },
    {
      icon: Waves,
      title: "Sound Engineering",
      description: "Professional audio system design and optimization",
    },
    {
      icon: Users,
      title: "Consultation Services",
      description: "Expert acoustic consulting for complex projects",
    },
  ]

  return (
    <section className="h-screen flex items-center justify-center bg-slate-800">
      <div className="max-w-6xl mx-auto px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl font-bold text-white mb-6">Our Services</h2>
          <p className="text-xl text-slate-300">Comprehensive acoustic solutions for every need</p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                className="bg-white/10 p-8 rounded-xl border border-slate-600 hover:border-blue-400 transition-all duration-300"
              >
                <IconComponent className="w-12 h-12 text-blue-400 mb-4" />
                <h3 className="text-2xl font-semibold text-white mb-4">{service.title}</h3>
                <p className="text-slate-300">{service.description}</p>
              </motion.div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

const processSteps = [
  {
    id: "discovery",
    icon: Search,
    title: "Discovery",
    description:
      "We begin by understanding your unique acoustic challenges and requirements through comprehensive site analysis and consultation.",
  },
  {
    id: "analysis",
    icon: BarChart3,
    title: "Analysis & Design",
    description:
      "Our experts analyze acoustic data and create custom solutions tailored to your specific environment and needs.",
  },
  {
    id: "implementation",
    icon: Settings,
    title: "Implementation",
    description:
      "Professional installation and integration of acoustic solutions with minimal disruption to your operations.",
  },
  {
    id: "verification",
    icon: CheckCircle,
    title: "Verification & Support",
    description:
      "Post-installation testing and ongoing support to ensure optimal acoustic performance and client satisfaction.",
  },
]

// Generate sine wave path with proper curves like the reference image
// Using fixed precision to avoid hydration mismatches
const generateSineWavePath = (width: number, height: number, frequency = 2, amplitude = 80) => {
  const points = []
  const steps = 300 // More steps for smoother curve
  for (let i = 0; i <= steps; i++) {
    const x = (i / steps) * width
    const y = height / 2 + Math.sin((i / steps) * Math.PI * frequency) * amplitude
    // Round to avoid hydration mismatches
    points.push(`${Math.round(x * 100) / 100},${Math.round(y * 100) / 100}`)
  }
  return `M ${points.join(" L ")}`
}

// Calculate position along sine wave for any progress value
const getPositionOnSineWave = (
  progress: number,
  totalWidth = 900,
  totalHeight = 400,
  frequency = 2,
  amplitude = 80,
) => {
  const x = progress * totalWidth
  const y = totalHeight / 2 + Math.sin(progress * Math.PI * frequency) * amplitude
  // Round to avoid hydration mismatches
  return {
    x: Math.round(x * 100) / 100,
    y: Math.round(y * 100) / 100
  }
}

function ProcessSection({
  scrollProgress,
  onAnimationComplete
}: {
  scrollProgress: any
  onAnimationComplete: () => void
}) {
  const [currentStep, setCurrentStep] = useState(-1)
  const [isAtStop, setIsAtStop] = useState(false)
  const [snappedProgress, setSnappedProgress] = useState(0)
  const [animationComplete, setAnimationComplete] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [internalScrollProgress, setInternalScrollProgress] = useState(0)

  const processProgress = useSpring(0, { stiffness: 100, damping: 30 })

  // Always call useTransform to avoid hooks order issues
  const currentPosition = useTransform(processProgress, (progress) => {
    return getPositionOnSineWave(progress, 900, 400, 2, 80)
  })

  // Pre-calculate transform values to avoid calling useTransform conditionally
  const iconCx = useTransform(currentPosition, (pos) => pos.x)
  const iconCy = useTransform(currentPosition, (pos) => pos.y)
  const iconX = useTransform(currentPosition, (pos) => pos.x - 18)
  const iconY = useTransform(currentPosition, (pos) => pos.y - 18)

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Manual scroll-controlled animation through the process steps
  useEffect(() => {
    const unsubscribe = scrollProgress.on("change", (latest: number) => {
      const waveProgress = Math.max(0, Math.min(1, latest))
      setInternalScrollProgress(waveProgress)

      // Always update the process progress to move the icon
      processProgress.set(waveProgress)

      // Determine which step we're closest to
      const stepProgress = waveProgress * (processSteps.length - 1)
      const nearestStep = Math.round(stepProgress)
      const distanceToStep = Math.abs(stepProgress - nearestStep)
      const snapThreshold = 0.15

      if (distanceToStep < snapThreshold && nearestStep >= 0 && nearestStep < processSteps.length) {
        // Snap to the step position
        const snappedStepProgress = nearestStep / (processSteps.length - 1)
        setSnappedProgress(snappedStepProgress)
        setCurrentStep(nearestStep)
        setIsAtStop(true)

        // Check if we've reached the end
        if (nearestStep === processSteps.length - 1 && !animationComplete) {
          setAnimationComplete(true)
          onAnimationComplete()
        }
      } else {
        // Follow scroll smoothly
        setSnappedProgress(waveProgress)
        setCurrentStep(-1)
        setIsAtStop(false)
      }
    })

    return unsubscribe
  }, [scrollProgress, processProgress, animationComplete, onAnimationComplete])

  const sineWavePath = generateSineWavePath(900, 400, 2, 80)

  // Calculate popup position based on sine wave position
  const getPopupPosition = (iconPosition: { x: number; y: number }) => {
    const centerY = 200 // Center of the sine wave (400/2)
    const isIconAtTop = iconPosition.y < centerY

    return {
      x: iconPosition.x,
      y: isIconAtTop ? iconPosition.y + 120 : iconPosition.y - 120, // Position opposite to icon
      isBelow: isIconAtTop,
    }
  }

  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-8 w-full">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-5xl font-bold text-white mb-6">Our Process</h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            A systematic approach to delivering exceptional acoustic solutions
          </p>
        </motion.div>

        <div className="relative flex justify-center w-full">
          {/* Container for sine wave and popups */}
          <div className="relative w-full max-w-5xl mx-auto px-4">
            {isClient && (
              <svg
                width="100%"
                height="400"
                viewBox="0 0 900 400"
                className="w-full h-auto"
                preserveAspectRatio="xMidYMid meet"
              >
              {/* Animated sine wave path */}
              <motion.path
                d={sineWavePath}
                stroke="url(#gradient)"
                strokeWidth="4"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 3, ease: "easeInOut" }}
              />

              {/* Gradient definition */}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="50%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#06b6d4" />
                </linearGradient>
              </defs>

              {/* Step position markers */}
              {processSteps.map((step, index) => {
                const stepProgress = index / (processSteps.length - 1)
                const position = getPositionOnSineWave(stepProgress, 900, 400, 2, 80)

                return (
                  <motion.circle
                    key={step.id}
                    cx={position.x}
                    cy={position.y}
                    r="8"
                    fill={currentStep === index ? "#3b82f6" : "#64748b"}
                    stroke={currentStep === index ? "#ffffff" : "transparent"}
                    strokeWidth="2"
                    initial={{ scale: 0 }}
                    animate={{
                      scale: 1,
                      fill: currentStep === index ? "#3b82f6" : "#64748b",
                    }}
                    transition={{ delay: index * 0.3, duration: 0.3 }}
                  />
                )
              })}

              {/* Moving icon that follows the sine wave */}
              <motion.g>
                <motion.circle
                  cx={iconCx}
                  cy={iconCy}
                  r="35"
                  fill="white"
                  stroke="#3b82f6"
                  strokeWidth="4"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, type: "spring" }}
                  style={{
                    filter: "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.4))",
                  }}
                />
                <motion.foreignObject
                  x={iconX}
                  y={iconY}
                  width="36"
                  height="36"
                >
                  <div className="w-full h-full flex items-center justify-center">
                    {React.createElement(currentStep >= 0 ? processSteps[currentStep]?.icon : Search, {
                      className: "w-9 h-9 text-blue-500",
                    })}
                  </div>
                </motion.foreignObject>
              </motion.g>
            </svg>
            )}

            {/* Fallback for server-side rendering */}
            {!isClient && (
              <div className="w-[900px] h-[300px] flex items-center justify-center bg-slate-800/50 rounded-lg">
                <div className="text-white text-lg">Loading process visualization...</div>
              </div>
            )}

            {/* Popup positioned relative to icon on sine wave */}
            <AnimatePresence mode="wait">
              {isClient && isAtStop && currentStep >= 0 && (
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 25,
                    duration: 0.4,
                  }}
                  className="absolute z-10 pointer-events-none"
                  style={{
                    left: `${getPositionOnSineWave(snappedProgress, 900, 400, 2, 80).x}px`,
                    top: `${getPopupPosition(getPositionOnSineWave(snappedProgress, 900, 400, 2, 80)).y}px`,
                    transform: "translate(-50%, -50%)",
                  }}
                >
                  <div className="bg-white/15 backdrop-blur-md p-6 rounded-2xl border border-blue-400/50 shadow-2xl shadow-blue-500/20 max-w-xs text-center">
                    <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center mb-4 mx-auto shadow-lg shadow-blue-500/50">
                      {React.createElement(processSteps[currentStep].icon, {
                        className: "w-6 h-6 text-white",
                      })}
                    </div>
                    <h3 className="text-lg font-bold text-white mb-3">{processSteps[currentStep].title}</h3>
                    <p className="text-slate-200 text-sm leading-relaxed">{processSteps[currentStep].description}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  )
}

function ContactSection() {
  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-slate-900 to-purple-900 relative overflow-hidden">
      <div className="max-w-4xl mx-auto px-8 text-center">
        <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
          <h2 className="text-5xl font-bold text-white mb-6">Get In Touch</h2>
          <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Ready to transform your space with expert acoustic solutions? Let's discuss your project.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Start Your Project
            </motion.button>
            <motion.button
              className="border border-white/30 hover:border-white/50 text-white px-8 py-4 rounded-lg font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              View Portfolio
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default function Component() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [currentSection, setCurrentSection] = useState(0)
  const [isProcessAnimationComplete, setIsProcessAnimationComplete] = useState(false)
  const [isScrollLocked, setIsScrollLocked] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const [isScrolling, setIsScrolling] = useState(false)
  const [processScrollProgress, setProcessScrollProgress] = useState(0)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const totalSections = 4

  // Handle scroll events for horizontal navigation with debouncing
  const handleScroll = useCallback((event: WheelEvent) => {
    event.preventDefault()

    const delta = event.deltaY || event.deltaX

    if (Math.abs(delta) < 30) return // Ignore small scroll movements

    // Special handling for process section (section 2)
    if (currentSection === 2) {
      // In process section, control the internal animation progress
      const scrollDirection = delta > 0 ? 1 : -1
      const scrollStep = 0.05 // Adjust sensitivity

      setProcessScrollProgress(prev => {
        const newProgress = Math.max(0, Math.min(1, prev + (scrollDirection * scrollStep)))

        // Only allow leaving the section when at the end and scrolling forward
        if (newProgress >= 1 && scrollDirection > 0 && !isScrolling) {
          setIsScrolling(true)
          setCurrentSection(3) // Move to next section

          // Reset scrolling state after delay
          scrollTimeoutRef.current = setTimeout(() => {
            setIsScrolling(false)
          }, 800)
        }
        // Allow going back to previous section when at start and scrolling backward
        else if (newProgress <= 0 && scrollDirection < 0 && !isScrolling) {
          setIsScrolling(true)
          setCurrentSection(1) // Move to previous section
          setProcessScrollProgress(0) // Reset progress

          // Reset scrolling state after delay
          scrollTimeoutRef.current = setTimeout(() => {
            setIsScrolling(false)
          }, 800)
        }

        return newProgress
      })
      return
    }

    // Normal section navigation for other sections
    if (isScrolling) return

    // Prevent rapid scrolling
    setIsScrolling(true)

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    if (delta > 0 && currentSection < totalSections - 1) {
      // Scrolling right/down
      setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
    } else if (delta < 0 && currentSection > 0) {
      // Scrolling left/up
      setCurrentSection(prev => Math.max(prev - 1, 0))
    }

    // Reset scrolling state after delay
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false)
    }, 800) // 800ms delay before allowing next scroll

  }, [currentSection, isScrolling, processScrollProgress])

  // Handle process animation completion
  const handleProcessAnimationComplete = useCallback(() => {
    setIsProcessAnimationComplete(true)
    setIsScrollLocked(false)
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault()
        if (currentSection === 2) {
          // In process section, advance the animation
          setProcessScrollProgress(prev => {
            const newProgress = Math.min(1, prev + 0.1)
            if (newProgress >= 1) {
              setCurrentSection(3) // Move to next section when complete
            }
            return newProgress
          })
        } else if (currentSection < totalSections - 1) {
          setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
        }
        break
      case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault()
        if (currentSection === 2) {
          // In process section, go back in the animation
          setProcessScrollProgress(prev => {
            const newProgress = Math.max(0, prev - 0.1)
            if (newProgress <= 0) {
              setCurrentSection(1) // Move to previous section when at start
              setProcessScrollProgress(0)
            }
            return newProgress
          })
        } else if (currentSection > 0) {
          setCurrentSection(prev => Math.max(prev - 1, 0))
        }
        break
      case 'Home':
        event.preventDefault()
        setCurrentSection(0)
        setProcessScrollProgress(0)
        break
      case 'End':
        event.preventDefault()
        setCurrentSection(totalSections - 1)
        setProcessScrollProgress(1)
        break
    }
  }, [currentSection, totalSections])

  // Handle touch events for mobile swipe navigation
  const handleTouchStart = useCallback((event: TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(event.targetTouches[0].clientX)
  }, [])

  const handleTouchMove = useCallback((event: TouchEvent) => {
    setTouchEnd(event.targetTouches[0].clientX)
  }, [])

  const handleTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd || isScrollLocked) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe && currentSection < totalSections - 1) {
      if (currentSection === 2 && !isProcessAnimationComplete) {
        setIsScrollLocked(true)
        return
      }
      setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
    }

    if (isRightSwipe && currentSection > 0) {
      setCurrentSection(prev => Math.max(prev - 1, 0))
    }
  }, [touchStart, touchEnd, currentSection, isProcessAnimationComplete, isScrollLocked, totalSections])

  // Add scroll, keyboard, and touch event listeners
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('wheel', handleScroll, { passive: false })
    container.addEventListener('touchstart', handleTouchStart, { passive: false })
    container.addEventListener('touchmove', handleTouchMove, { passive: false })
    container.addEventListener('touchend', handleTouchEnd, { passive: false })
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      container.removeEventListener('wheel', handleScroll)
      container.removeEventListener('touchstart', handleTouchStart)
      container.removeEventListener('touchmove', handleTouchMove)
      container.removeEventListener('touchend', handleTouchEnd)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleScroll, handleKeyDown, handleTouchStart, handleTouchMove, handleTouchEnd])

  // Calculate transform based on current section
  const xTransform = `translateX(-${currentSection * 100}vw)`

  // Create scroll progress spring for process section
  const processScrollProgressSpring = useSpring(processScrollProgress, { stiffness: 100, damping: 30 })

  // Reset process progress when entering/leaving process section
  useEffect(() => {
    if (currentSection === 2) {
      // Entering process section - start from beginning if coming from left, or maintain progress
      if (processScrollProgress === 0) {
        setProcessScrollProgress(0)
      }
    } else if (currentSection < 2) {
      // Going to earlier sections - reset progress
      setProcessScrollProgress(0)
    }
  }, [currentSection, processScrollProgress])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className="h-screen overflow-hidden relative"
      style={{
        cursor: isScrollLocked ? 'not-allowed' : (isScrolling ? 'grabbing' : 'grab')
      }}
    >
      {/* Horizontal sections container */}
      <div
        className="flex h-screen transition-transform duration-1000 ease-out"
        style={{
          transform: xTransform,
          width: `${totalSections * 100}vw`
        }}
      >
        {/* Section 1: Hero */}
        <div className="w-screen h-screen flex-shrink-0">
          <HeroSection />
        </div>

        {/* Section 2: Services */}
        <div className="w-screen h-screen flex-shrink-0">
          <ServicesSection />
        </div>

        {/* Section 3: Process */}
        <div className="w-screen h-screen flex-shrink-0 relative">
          <ProcessSection
            scrollProgress={processScrollProgressSpring}
            onAnimationComplete={handleProcessAnimationComplete}
          />
        </div>

        {/* Section 4: Contact */}
        <div className="w-screen h-screen flex-shrink-0">
          <ContactSection />
        </div>
      </div>

      {/* Section indicators */}
      <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-50">
        {Array.from({ length: totalSections }).map((_, index) => (
          <button
            key={index}
            onClick={() => {
              setCurrentSection(index)
              if (index !== 2) {
                setProcessScrollProgress(index > 2 ? 1 : 0)
              }
            }}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              currentSection === index
                ? 'bg-blue-500 scale-125'
                : 'bg-white/50 hover:bg-white/70'
            }`}
            title={`Section ${index + 1}`}
          />
        ))}
      </div>

      {/* Process section progress indicator */}
      {currentSection === 2 && (
        <div className="fixed bottom-16 left-1/2 transform -translate-x-1/2 z-40">
          <div className="bg-black/60 text-white px-4 py-2 rounded-lg backdrop-blur-sm text-sm">
            <div className="flex items-center gap-2">
              <span>Process Progress:</span>
              <div className="w-20 h-2 bg-gray-600 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-500 transition-all duration-300"
                  style={{ width: `${processScrollProgress * 100}%` }}
                />
              </div>
              <span>{Math.round(processScrollProgress * 100)}%</span>
            </div>
          </div>
        </div>
      )}

      {/* Navigation hint */}
      {!isScrolling && currentSection !== 2 && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-40">
          <div className="bg-black/60 text-white px-4 py-2 rounded-lg backdrop-blur-sm text-sm text-center">
            <div className="hidden md:block">Use scroll wheel, arrow keys, or dots to navigate</div>
            <div className="md:hidden">Swipe left/right or tap dots to navigate</div>
          </div>
        </div>
      )}

      {/* Process section navigation hint */}
      {currentSection === 2 && !isScrolling && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-40">
          <div className="bg-purple-600/80 text-white px-4 py-2 rounded-lg backdrop-blur-sm text-sm text-center">
            <div className="hidden md:block">Scroll to control the process animation</div>
            <div className="md:hidden">Swipe to control the process animation</div>
          </div>
        </div>
      )}

      {/* Scroll cooldown indicator */}
      {isScrolling && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-40">
          <div className="bg-blue-600/80 text-white px-4 py-2 rounded-lg backdrop-blur-sm text-sm text-center">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              Scrolling...
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
