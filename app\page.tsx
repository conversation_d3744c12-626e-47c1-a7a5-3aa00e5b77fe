"use client"

import React from "react"

import { useEffect, useRef, useState } from "react"
import { motion, useScroll, useTransform, useSpring, AnimatePresence } from "framer-motion"
import { Search, BarChart3, Settings, CheckCircle, ArrowRight, Volume2, Waves, Building, Users } from "lucide-react"

function HeroSection() {
  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/placeholder.svg?height=1080&width=1920')] bg-cover bg-center opacity-10" />
      <div className="relative z-10 text-center max-w-4xl mx-auto px-8">
        <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 1 }}>
          <Volume2 className="w-16 h-16 text-blue-400 mx-auto mb-6" />
          <h1 className="text-6xl font-bold text-white mb-6"><PERSON> Acoustics</h1>
          <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Transforming spaces through innovative acoustic solutions and expert sound engineering
          </p>
          <motion.button
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold flex items-center gap-2 mx-auto"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Explore Our Work <ArrowRight className="w-5 h-5" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

function ServicesSection() {
  const services = [
    {
      icon: Building,
      title: "Architectural Acoustics",
      description: "Custom acoustic design for commercial and residential spaces",
    },
    {
      icon: Waves,
      title: "Sound Engineering",
      description: "Professional audio system design and optimization",
    },
    {
      icon: Users,
      title: "Consultation Services",
      description: "Expert acoustic consulting for complex projects",
    },
  ]

  return (
    <section className="h-screen flex items-center justify-center bg-slate-800">
      <div className="max-w-6xl mx-auto px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl font-bold text-white mb-6">Our Services</h2>
          <p className="text-xl text-slate-300">Comprehensive acoustic solutions for every need</p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                className="bg-white/10 p-8 rounded-xl border border-slate-600 hover:border-blue-400 transition-all duration-300"
              >
                <IconComponent className="w-12 h-12 text-blue-400 mb-4" />
                <h3 className="text-2xl font-semibold text-white mb-4">{service.title}</h3>
                <p className="text-slate-300">{service.description}</p>
              </motion.div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

const processSteps = [
  {
    id: "discovery",
    icon: Search,
    title: "Discovery",
    description:
      "We begin by understanding your unique acoustic challenges and requirements through comprehensive site analysis and consultation.",
  },
  {
    id: "analysis",
    icon: BarChart3,
    title: "Analysis & Design",
    description:
      "Our experts analyze acoustic data and create custom solutions tailored to your specific environment and needs.",
  },
  {
    id: "implementation",
    icon: Settings,
    title: "Implementation",
    description:
      "Professional installation and integration of acoustic solutions with minimal disruption to your operations.",
  },
  {
    id: "verification",
    icon: CheckCircle,
    title: "Verification & Support",
    description:
      "Post-installation testing and ongoing support to ensure optimal acoustic performance and client satisfaction.",
  },
]

// Generate sine wave path with proper curves like the reference image
const generateSineWavePath = (width: number, height: number, frequency = 2, amplitude = 100) => {
  const points = []
  const steps = 300 // More steps for smoother curve
  for (let i = 0; i <= steps; i++) {
    const x = (i / steps) * width
    const y = height / 2 + Math.sin((i / steps) * Math.PI * frequency) * amplitude
    points.push(`${x},${y}`)
  }
  return `M ${points.join(" L ")}`
}

// Calculate position along sine wave for any progress value
const getPositionOnSineWave = (
  progress: number,
  totalWidth = 900,
  totalHeight = 300,
  frequency = 2,
  amplitude = 100,
) => {
  const x = progress * totalWidth
  const y = totalHeight / 2 + Math.sin(progress * Math.PI * frequency) * amplitude
  return { x, y }
}

function ProcessSection({ scrollProgress }: { scrollProgress: any }) {
  const [currentStep, setCurrentStep] = useState(-1)
  const [isAtStop, setIsAtStop] = useState(false)
  const [snappedProgress, setSnappedProgress] = useState(0)

  const processProgress = useSpring(0, { stiffness: 100, damping: 30 })

  useEffect(() => {
    const unsubscribe = scrollProgress.on("change", (latest: number) => {
      const waveProgress = Math.max(0, Math.min(1, latest))

      // Determine which step we're closest to
      const stepProgress = waveProgress * (processSteps.length - 1)
      const nearestStep = Math.round(stepProgress)
      const distanceToStep = Math.abs(stepProgress - nearestStep)
      const snapThreshold = 0.15

      if (distanceToStep < snapThreshold && nearestStep >= 0 && nearestStep < processSteps.length) {
        // Snap to the step position
        const snappedStepProgress = nearestStep / (processSteps.length - 1)
        setSnappedProgress(snappedStepProgress)
        processProgress.set(snappedStepProgress)
        setCurrentStep(nearestStep)
        setIsAtStop(true)
      } else {
        // Follow normal scroll
        setSnappedProgress(waveProgress)
        processProgress.set(waveProgress)
        setCurrentStep(-1)
        setIsAtStop(false)
      }
    })

    return unsubscribe
  }, [scrollProgress, processProgress])

  const sineWavePath = generateSineWavePath(900, 300, 2, 100)

  // Calculate the current position along the sine wave
  const currentPosition = useTransform(processProgress, (progress) => {
    return getPositionOnSineWave(progress, 900, 300, 2, 100)
  })

  // Calculate popup position based on sine wave position
  const getPopupPosition = (iconPosition: { x: number; y: number }) => {
    const centerY = 150 // Center of the sine wave (300/2)
    const isIconAtTop = iconPosition.y < centerY

    return {
      x: iconPosition.x,
      y: isIconAtTop ? iconPosition.y + 120 : iconPosition.y - 120, // Position opposite to icon
      isBelow: isIconAtTop,
    }
  }

  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-8 w-full">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-5xl font-bold text-white mb-6">Our Process</h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            A systematic approach to delivering exceptional acoustic solutions
          </p>
        </motion.div>

        <div className="relative flex justify-center">
          {/* Container for sine wave and popups */}
          <div className="relative">
            <svg width="900" height="300" viewBox="0 0 900 300" className="max-w-full">
              {/* Animated sine wave path */}
              <motion.path
                d={sineWavePath}
                stroke="url(#gradient)"
                strokeWidth="4"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 3, ease: "easeInOut" }}
              />

              {/* Gradient definition */}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="50%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#06b6d4" />
                </linearGradient>
              </defs>

              {/* Step position markers */}
              {processSteps.map((step, index) => {
                const stepProgress = index / (processSteps.length - 1)
                const position = getPositionOnSineWave(stepProgress, 900, 300, 2, 100)

                return (
                  <motion.circle
                    key={step.id}
                    cx={position.x}
                    cy={position.y}
                    r="8"
                    fill={currentStep === index ? "#3b82f6" : "#64748b"}
                    stroke={currentStep === index ? "#ffffff" : "transparent"}
                    strokeWidth="2"
                    initial={{ scale: 0 }}
                    animate={{
                      scale: 1,
                      fill: currentStep === index ? "#3b82f6" : "#64748b",
                    }}
                    transition={{ delay: index * 0.3, duration: 0.3 }}
                  />
                )
              })}

              {/* Moving icon that follows the sine wave */}
              <motion.g>
                <motion.circle
                  cx={useTransform(currentPosition, (pos) => pos.x)}
                  cy={useTransform(currentPosition, (pos) => pos.y)}
                  r="35"
                  fill="white"
                  stroke="#3b82f6"
                  strokeWidth="4"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, type: "spring" }}
                  style={{
                    filter: "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.4))",
                  }}
                />
                <motion.foreignObject
                  x={useTransform(currentPosition, (pos) => pos.x - 18)}
                  y={useTransform(currentPosition, (pos) => pos.y - 18)}
                  width="36"
                  height="36"
                >
                  <div className="w-full h-full flex items-center justify-center">
                    {React.createElement(currentStep >= 0 ? processSteps[currentStep]?.icon : Search, {
                      className: "w-9 h-9 text-blue-500",
                    })}
                  </div>
                </motion.foreignObject>
              </motion.g>
            </svg>

            {/* Popup positioned relative to icon on sine wave */}
            <AnimatePresence mode="wait">
              {isAtStop && currentStep >= 0 && (
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 25,
                    duration: 0.4,
                  }}
                  className="absolute z-10 pointer-events-none"
                  style={{
                    left: `${getPositionOnSineWave(snappedProgress, 900, 300, 2, 100).x}px`,
                    top: `${getPopupPosition(getPositionOnSineWave(snappedProgress, 900, 300, 2, 100)).y}px`,
                    transform: "translate(-50%, -50%)",
                  }}
                >
                  <div className="bg-white/15 backdrop-blur-md p-6 rounded-2xl border border-blue-400/50 shadow-2xl shadow-blue-500/20 max-w-xs text-center">
                    <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center mb-4 mx-auto shadow-lg shadow-blue-500/50">
                      {React.createElement(processSteps[currentStep].icon, {
                        className: "w-6 h-6 text-white",
                      })}
                    </div>
                    <h3 className="text-lg font-bold text-white mb-3">{processSteps[currentStep].title}</h3>
                    <p className="text-slate-200 text-sm leading-relaxed">{processSteps[currentStep].description}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  )
}

function ContactSection() {
  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-slate-900 to-purple-900 relative overflow-hidden">
      <div className="max-w-4xl mx-auto px-8 text-center">
        <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
          <h2 className="text-5xl font-bold text-white mb-6">Get In Touch</h2>
          <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Ready to transform your space with expert acoustic solutions? Let's discuss your project.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Start Your Project
            </motion.button>
            <motion.button
              className="border border-white/30 hover:border-white/50 text-white px-8 py-4 rounded-lg font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              View Portfolio
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default function Component() {
  const containerRef = useRef<HTMLDivElement>(null);

  // Use horizontal scroll
  const { scrollYProgress } = useScroll({
    container: containerRef,
    axis: "x", // Change axis to horizontal
    offset: ["start start", "end end"],
  });

  // Map horizontal scroll to section positions
  const xTransform = useTransform(scrollYProgress, [0, 1], ["0%", "-300%"]); // Adjust range based on 4 sections

  // Calculate process section scroll progress range
  // Assuming 4 sections, ProcessSection is the 3rd one, covering 50%-75% of horizontal scroll
  const processScrollProgress = useTransform(scrollYProgress, [0.5, 0.75], [0, 1]);

  return (
    // Main container with horizontal scrolling
    <div ref={containerRef} className="flex w-[400vw] overflow-x-scroll overflow-y-hidden">
      {/* Wrap sections in a motion.div for horizontal animation */}
      <motion.div className="flex h-screen" style={{ x: xTransform }}>
        {/* Section 1: Hero */}
        <div className="w-screen h-screen flex-shrink-0">
          <HeroSection />
        </div>

        {/* Section 2: Services */}
        <div className="w-screen h-screen flex-shrink-0">
          <ServicesSection />
        </div>

        {/* Section 3: Process */}
        {/* This section needs to stick while its animation runs */}
        <div className="w-screen h-screen flex-shrink-0 relative">
          <ProcessSection scrollProgress={processScrollProgress} />
        </div>

        {/* Section 4: Contact */}
        <div className="w-screen h-screen flex-shrink-0">
          <ContactSection />
        </div>
      </motion.div>
    </div>
  );
}
