"use client"

import React from "react"

import { useEffect, useRef, useState, useCallback } from "react"
import { motion, useScroll, useTransform, useSpring, AnimatePresence } from "framer-motion"
import { Search, BarChart3, Settings, CheckCircle, ArrowRight, Volume2, Waves, Building, Users } from "lucide-react"

function HeroSection() {
  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/placeholder.svg?height=1080&width=1920')] bg-cover bg-center opacity-10" />
      <div className="relative z-10 text-center max-w-4xl mx-auto px-8">
        <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 1 }}>
          <Volume2 className="w-16 h-16 text-blue-400 mx-auto mb-6" />
          <h1 className="text-6xl font-bold text-white mb-6">Porter Acoustics</h1>
          <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Transforming spaces through innovative acoustic solutions and expert sound engineering
          </p>
          <motion.button
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold flex items-center gap-2 mx-auto"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Explore Our Work <ArrowRight className="w-5 h-5" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

function ServicesSection() {
  const services = [
    {
      icon: Building,
      title: "Architectural Acoustics",
      description: "Custom acoustic design for commercial and residential spaces",
    },
    {
      icon: Waves,
      title: "Sound Engineering",
      description: "Professional audio system design and optimization",
    },
    {
      icon: Users,
      title: "Consultation Services",
      description: "Expert acoustic consulting for complex projects",
    },
  ]

  return (
    <section className="h-screen flex items-center justify-center bg-slate-800">
      <div className="max-w-6xl mx-auto px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl font-bold text-white mb-6">Our Services</h2>
          <p className="text-xl text-slate-300">Comprehensive acoustic solutions for every need</p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                className="bg-white/10 p-8 rounded-xl border border-slate-600 hover:border-blue-400 transition-all duration-300"
              >
                <IconComponent className="w-12 h-12 text-blue-400 mb-4" />
                <h3 className="text-2xl font-semibold text-white mb-4">{service.title}</h3>
                <p className="text-slate-300">{service.description}</p>
              </motion.div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

const processSteps = [
  {
    id: "discovery",
    icon: Search,
    title: "Discovery",
    description:
      "We begin by understanding your unique acoustic challenges and requirements through comprehensive site analysis and consultation.",
  },
  {
    id: "analysis",
    icon: BarChart3,
    title: "Analysis & Design",
    description:
      "Our experts analyze acoustic data and create custom solutions tailored to your specific environment and needs.",
  },
  {
    id: "implementation",
    icon: Settings,
    title: "Implementation",
    description:
      "Professional installation and integration of acoustic solutions with minimal disruption to your operations.",
  },
  {
    id: "verification",
    icon: CheckCircle,
    title: "Verification & Support",
    description:
      "Post-installation testing and ongoing support to ensure optimal acoustic performance and client satisfaction.",
  },
]

// Generate sine wave path with proper curves like the reference image
const generateSineWavePath = (width: number, height: number, frequency = 2, amplitude = 100) => {
  const points = []
  const steps = 300 // More steps for smoother curve
  for (let i = 0; i <= steps; i++) {
    const x = (i / steps) * width
    const y = height / 2 + Math.sin((i / steps) * Math.PI * frequency) * amplitude
    points.push(`${x},${y}`)
  }
  return `M ${points.join(" L ")}`
}

// Calculate position along sine wave for any progress value
const getPositionOnSineWave = (
  progress: number,
  totalWidth = 900,
  totalHeight = 300,
  frequency = 2,
  amplitude = 100,
) => {
  const x = progress * totalWidth
  const y = totalHeight / 2 + Math.sin(progress * Math.PI * frequency) * amplitude
  return { x, y }
}

function ProcessSection({
  scrollProgress,
  onAnimationComplete
}: {
  scrollProgress: any
  onAnimationComplete: () => void
}) {
  const [currentStep, setCurrentStep] = useState(-1)
  const [isAtStop, setIsAtStop] = useState(false)
  const [snappedProgress, setSnappedProgress] = useState(0)
  const [animationComplete, setAnimationComplete] = useState(false)

  const processProgress = useSpring(0, { stiffness: 100, damping: 30 })

  // Auto-animate through the process steps when section is active
  useEffect(() => {
    let timeoutId: NodeJS.Timeout
    let currentStepIndex = 0

    const animateToNextStep = () => {
      if (currentStepIndex < processSteps.length) {
        const stepProgress = currentStepIndex / (processSteps.length - 1)
        setSnappedProgress(stepProgress)
        processProgress.set(stepProgress)
        setCurrentStep(currentStepIndex)
        setIsAtStop(true)

        currentStepIndex++

        if (currentStepIndex < processSteps.length) {
          timeoutId = setTimeout(animateToNextStep, 2000) // 2 seconds per step
        } else {
          // Animation complete
          setTimeout(() => {
            setAnimationComplete(true)
            onAnimationComplete()
          }, 2000) // Wait 2 seconds on final step
        }
      }
    }

    // Start animation when scroll progress indicates we're in this section
    const unsubscribe = scrollProgress.on("change", (latest: number) => {
      if (latest > 0.5 && !animationComplete && currentStepIndex === 0) {
        animateToNextStep()
      }
    })

    return () => {
      unsubscribe()
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [scrollProgress, processProgress, animationComplete, onAnimationComplete])

  const sineWavePath = generateSineWavePath(900, 300, 2, 100)

  // Calculate the current position along the sine wave
  const currentPosition = useTransform(processProgress, (progress) => {
    return getPositionOnSineWave(progress, 900, 300, 2, 100)
  })

  // Calculate popup position based on sine wave position
  const getPopupPosition = (iconPosition: { x: number; y: number }) => {
    const centerY = 150 // Center of the sine wave (300/2)
    const isIconAtTop = iconPosition.y < centerY

    return {
      x: iconPosition.x,
      y: isIconAtTop ? iconPosition.y + 120 : iconPosition.y - 120, // Position opposite to icon
      isBelow: isIconAtTop,
    }
  }

  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-8 w-full">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-5xl font-bold text-white mb-6">Our Process</h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            A systematic approach to delivering exceptional acoustic solutions
          </p>
        </motion.div>

        <div className="relative flex justify-center">
          {/* Container for sine wave and popups */}
          <div className="relative">
            <svg width="900" height="300" viewBox="0 0 900 300" className="max-w-full">
              {/* Animated sine wave path */}
              <motion.path
                d={sineWavePath}
                stroke="url(#gradient)"
                strokeWidth="4"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 3, ease: "easeInOut" }}
              />

              {/* Gradient definition */}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="50%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#06b6d4" />
                </linearGradient>
              </defs>

              {/* Step position markers */}
              {processSteps.map((step, index) => {
                const stepProgress = index / (processSteps.length - 1)
                const position = getPositionOnSineWave(stepProgress, 900, 300, 2, 100)

                return (
                  <motion.circle
                    key={step.id}
                    cx={position.x}
                    cy={position.y}
                    r="8"
                    fill={currentStep === index ? "#3b82f6" : "#64748b"}
                    stroke={currentStep === index ? "#ffffff" : "transparent"}
                    strokeWidth="2"
                    initial={{ scale: 0 }}
                    animate={{
                      scale: 1,
                      fill: currentStep === index ? "#3b82f6" : "#64748b",
                    }}
                    transition={{ delay: index * 0.3, duration: 0.3 }}
                  />
                )
              })}

              {/* Moving icon that follows the sine wave */}
              <motion.g>
                <motion.circle
                  cx={useTransform(currentPosition, (pos) => pos.x)}
                  cy={useTransform(currentPosition, (pos) => pos.y)}
                  r="35"
                  fill="white"
                  stroke="#3b82f6"
                  strokeWidth="4"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, type: "spring" }}
                  style={{
                    filter: "drop-shadow(0 4px 12px rgba(59, 130, 246, 0.4))",
                  }}
                />
                <motion.foreignObject
                  x={useTransform(currentPosition, (pos) => pos.x - 18)}
                  y={useTransform(currentPosition, (pos) => pos.y - 18)}
                  width="36"
                  height="36"
                >
                  <div className="w-full h-full flex items-center justify-center">
                    {React.createElement(currentStep >= 0 ? processSteps[currentStep]?.icon : Search, {
                      className: "w-9 h-9 text-blue-500",
                    })}
                  </div>
                </motion.foreignObject>
              </motion.g>
            </svg>

            {/* Popup positioned relative to icon on sine wave */}
            <AnimatePresence mode="wait">
              {isAtStop && currentStep >= 0 && (
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 25,
                    duration: 0.4,
                  }}
                  className="absolute z-10 pointer-events-none"
                  style={{
                    left: `${getPositionOnSineWave(snappedProgress, 900, 300, 2, 100).x}px`,
                    top: `${getPopupPosition(getPositionOnSineWave(snappedProgress, 900, 300, 2, 100)).y}px`,
                    transform: "translate(-50%, -50%)",
                  }}
                >
                  <div className="bg-white/15 backdrop-blur-md p-6 rounded-2xl border border-blue-400/50 shadow-2xl shadow-blue-500/20 max-w-xs text-center">
                    <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center mb-4 mx-auto shadow-lg shadow-blue-500/50">
                      {React.createElement(processSteps[currentStep].icon, {
                        className: "w-6 h-6 text-white",
                      })}
                    </div>
                    <h3 className="text-lg font-bold text-white mb-3">{processSteps[currentStep].title}</h3>
                    <p className="text-slate-200 text-sm leading-relaxed">{processSteps[currentStep].description}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  )
}

function ContactSection() {
  return (
    <section className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-slate-900 to-purple-900 relative overflow-hidden">
      <div className="max-w-4xl mx-auto px-8 text-center">
        <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
          <h2 className="text-5xl font-bold text-white mb-6">Get In Touch</h2>
          <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Ready to transform your space with expert acoustic solutions? Let's discuss your project.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Start Your Project
            </motion.button>
            <motion.button
              className="border border-white/30 hover:border-white/50 text-white px-8 py-4 rounded-lg font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              View Portfolio
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default function Component() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [currentSection, setCurrentSection] = useState(0)
  const [isProcessAnimationComplete, setIsProcessAnimationComplete] = useState(false)
  const [isScrollLocked, setIsScrollLocked] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const totalSections = 4

  // Handle scroll events for horizontal navigation
  const handleScroll = useCallback((event: WheelEvent) => {
    if (isScrollLocked) {
      event.preventDefault()
      return
    }

    event.preventDefault()

    const delta = event.deltaY || event.deltaX

    if (Math.abs(delta) < 10) return // Ignore small scroll movements

    if (delta > 0 && currentSection < totalSections - 1) {
      // Scrolling right/down
      if (currentSection === 2 && !isProcessAnimationComplete) {
        // Lock scroll in process section until animation completes
        setIsScrollLocked(true)
        return
      }
      setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
    } else if (delta < 0 && currentSection > 0) {
      // Scrolling left/up
      setCurrentSection(prev => Math.max(prev - 1, 0))
    }
  }, [currentSection, isProcessAnimationComplete, isScrollLocked])

  // Handle process animation completion
  const handleProcessAnimationComplete = useCallback(() => {
    setIsProcessAnimationComplete(true)
    setIsScrollLocked(false)
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (isScrollLocked) return

    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault()
        if (currentSection < totalSections - 1) {
          if (currentSection === 2 && !isProcessAnimationComplete) {
            setIsScrollLocked(true)
            return
          }
          setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
        }
        break
      case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault()
        if (currentSection > 0) {
          setCurrentSection(prev => Math.max(prev - 1, 0))
        }
        break
      case 'Home':
        event.preventDefault()
        setCurrentSection(0)
        break
      case 'End':
        event.preventDefault()
        if (isProcessAnimationComplete) {
          setCurrentSection(totalSections - 1)
        }
        break
    }
  }, [currentSection, isProcessAnimationComplete, isScrollLocked, totalSections])

  // Handle touch events for mobile swipe navigation
  const handleTouchStart = useCallback((event: TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(event.targetTouches[0].clientX)
  }, [])

  const handleTouchMove = useCallback((event: TouchEvent) => {
    setTouchEnd(event.targetTouches[0].clientX)
  }, [])

  const handleTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd || isScrollLocked) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe && currentSection < totalSections - 1) {
      if (currentSection === 2 && !isProcessAnimationComplete) {
        setIsScrollLocked(true)
        return
      }
      setCurrentSection(prev => Math.min(prev + 1, totalSections - 1))
    }

    if (isRightSwipe && currentSection > 0) {
      setCurrentSection(prev => Math.max(prev - 1, 0))
    }
  }, [touchStart, touchEnd, currentSection, isProcessAnimationComplete, isScrollLocked, totalSections])

  // Add scroll, keyboard, and touch event listeners
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('wheel', handleScroll, { passive: false })
    container.addEventListener('touchstart', handleTouchStart, { passive: false })
    container.addEventListener('touchmove', handleTouchMove, { passive: false })
    container.addEventListener('touchend', handleTouchEnd, { passive: false })
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      container.removeEventListener('wheel', handleScroll)
      container.removeEventListener('touchstart', handleTouchStart)
      container.removeEventListener('touchmove', handleTouchMove)
      container.removeEventListener('touchend', handleTouchEnd)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleScroll, handleKeyDown, handleTouchStart, handleTouchMove, handleTouchEnd])

  // Calculate transform based on current section
  const xTransform = `translateX(-${currentSection * 100}vw)`

  // Create scroll progress for process section
  const processScrollProgress = useSpring(0, { stiffness: 100, damping: 30 })

  // Update process scroll progress when in process section
  useEffect(() => {
    if (currentSection === 2) {
      // Start process animation when entering section 2
      processScrollProgress.set(1)
      setIsScrollLocked(true) // Lock scroll immediately when entering process section
    } else {
      processScrollProgress.set(0)
    }
  }, [currentSection, processScrollProgress])

  return (
    <div
      ref={containerRef}
      className="h-screen overflow-hidden relative"
      style={{ cursor: isScrollLocked ? 'wait' : 'default' }}
    >
      {/* Horizontal sections container */}
      <div
        className="flex h-screen transition-transform duration-1000 ease-out"
        style={{
          transform: xTransform,
          width: `${totalSections * 100}vw`
        }}
      >
        {/* Section 1: Hero */}
        <div className="w-screen h-screen flex-shrink-0">
          <HeroSection />
        </div>

        {/* Section 2: Services */}
        <div className="w-screen h-screen flex-shrink-0">
          <ServicesSection />
        </div>

        {/* Section 3: Process */}
        <div className="w-screen h-screen flex-shrink-0 relative">
          <ProcessSection
            scrollProgress={processScrollProgress}
            onAnimationComplete={handleProcessAnimationComplete}
          />
        </div>

        {/* Section 4: Contact */}
        <div className="w-screen h-screen flex-shrink-0">
          <ContactSection />
        </div>
      </div>

      {/* Section indicators */}
      <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-50">
        {Array.from({ length: totalSections }).map((_, index) => (
          <button
            key={index}
            onClick={() => {
              if (index === 2 && !isProcessAnimationComplete) return
              setCurrentSection(index)
            }}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              currentSection === index
                ? 'bg-blue-500 scale-125'
                : 'bg-white/50 hover:bg-white/70'
            } ${index === 2 && !isProcessAnimationComplete ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={index === 2 && !isProcessAnimationComplete}
            title={`Section ${index + 1}${index === 2 && !isProcessAnimationComplete ? ' (Complete process animation first)' : ''}`}
          />
        ))}
      </div>

      {/* Navigation hint */}
      {!isScrollLocked && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-40">
          <div className="bg-black/60 text-white px-4 py-2 rounded-lg backdrop-blur-sm text-sm text-center">
            <div className="hidden md:block">Use scroll wheel, arrow keys, or dots to navigate</div>
            <div className="md:hidden">Swipe left/right or tap dots to navigate</div>
          </div>
        </div>
      )}

      {/* Process section lock indicator */}
      {isScrollLocked && (
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
          <div className="bg-black/80 text-white px-6 py-3 rounded-lg backdrop-blur-sm">
            <p className="text-sm">Follow the process animation to continue...</p>
          </div>
        </div>
      )}
    </div>
  )
}
